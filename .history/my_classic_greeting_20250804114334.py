from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.textinput import TextInput
from kivy.uix.screenmanager import ScreenManager, Screen


# 定义第一个屏幕（输入屏幕）
class InputScreen(Screen):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)  # 调用父类构造函数初始化屏幕

        # 创建水平布局容器（左边文字 + 右边输入框）
        layout = BoxLayout(orientation='horizontal', padding=20, spacing=20)

        # 创建左侧标签
        name_label = Label(
            text="What's your name?",  # 显示文本
            font_size=40,  # 字体大小
            halign='right',  # 水平右对齐
            size_hint_x=0.5  # 宽度占布局的50%
        )

        # 创建右侧文本输入框
        self.name_input = TextInput(
            multiline=False,  # 单行输入
            font_size=40,  # 字体大小
            size_hint_x=0.5,  # 宽度占布局的50%
            hint_text="Please enter your name",  # 提示文本
            write_tab=False  # 禁用Tab键切换焦点
        )
        # 绑定回车事件：当用户按下回车键时调用self.submit_name方法
        self.name_input.bind(on_text_validate=self.submit_name)

        # 将标签和输入框添加到布局
        layout.add_widget(name_label)
        layout.add_widget(self.name_input)

        # 将布局添加到当前屏幕
        self.add_widget(layout)

    def submit_name(self, instance):
        """处理姓名提交"""
        # 获取输入的姓名（去除首尾空格）
        name = self.name_input.text.strip()

        if name:  # 如果姓名不为空
            # 获取屏幕管理器
            sm = self.manager
            # 切换到欢迎屏幕
            sm.current = 'welcome'
            # 更新欢迎屏幕上的姓名显示
            sm.get_screen('welcome').update_name(name)


# 定义第二个屏幕（欢迎屏幕）
class WelcomeScreen(Screen):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)  # 调用父类构造函数初始化屏幕

        # 创建垂直布局容器
        layout = BoxLayout(orientation='vertical')

        # 创建欢迎标签（初始无内容）
        self.welcome_label = Label(
            font_size=50,  # 字体大小
            halign='center',  # 水平居中
            valign='middle'  # 垂直居中
        )

        # 将标签添加到布局
        layout.add_widget(self.welcome_label)
        # 将布局添加到当前屏幕
        self.add_widget(layout)

    def update_name(self, name):
        """更新显示的欢迎语"""
        self.welcome_label.text = f"你好，{name}！"


# 主应用类
class NameApp(App):
    def build(self):
        """创建应用主界面"""
        # 创建屏幕管理器（用于管理多个屏幕）
        sm = ScreenManager()

        # 创建输入屏幕并命名为'input'
        input_screen = InputScreen(name='input')
        # 创建欢迎屏幕并命名为'welcome'
        welcome_screen = WelcomeScreen(name='welcome')

        # 将两个屏幕添加到屏幕管理器
        sm.add_widget(input_screen)
        sm.add_widget(welcome_screen)

        # 设置当前显示输入屏幕
        sm.current = 'input'

        return sm  # 返回屏幕管理器作为根组件


# 运行应用
if __name__ == '__main__':
    NameApp().run()